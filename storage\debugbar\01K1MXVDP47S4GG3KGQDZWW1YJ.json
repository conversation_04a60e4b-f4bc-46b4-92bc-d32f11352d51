{"__meta": {"id": "01K1MXVDP47S4GG3KGQDZWW1YJ", "datetime": "2025-08-02 08:19:54", "utime": **********.693539, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"count": 3, "start": 1754122793.985926, "end": **********.693573, "duration": 0.7076470851898193, "duration_str": "708ms", "measures": [{"label": "Booting", "start": 1754122793.985926, "relative_start": 0, "end": **********.603397, "relative_end": **********.603397, "duration": 0.****************, "duration_str": "617ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.603432, "relative_start": 0.****************, "end": **********.693577, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "90.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.634256, "relative_start": 0.****************, "end": **********.668583, "relative_end": **********.668583, "duration": 0.034327030181884766, "duration_str": "34.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3096\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#3387 …}\n  file: \"C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\routes\\web.php\"\n  line: \"1916 to 1922\"\n}", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Froutes%2Fweb.php&line=1916\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:1916-1922</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "weKCCyBOnIIfsHgeYskf9pteVjEqxb3Mn8KV5VEp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752...", "action_name": null, "controller_action": "Closure", "uri": "GET storage/{path}", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Froutes%2Fweb.php&line=1916\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:1916-1922</a>", "middleware": "web, verified", "duration": "715ms", "peak_memory": "50MB", "response": "Redirect to http://localhost:8000/verify", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-129166504 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-129166504\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1128337807 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1128337807\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-427988486 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427988486\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1259237397 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1259237397\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-455623408 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:19:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455623408\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1770830595 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">weKCCyBOnIIfsHgeYskf9pteVjEqxb3Mn8KV5VEp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770830595\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752...", "controller_action": "Closure"}, "badge": "302 Found"}}