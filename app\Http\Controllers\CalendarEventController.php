<?php

namespace App\Http\Controllers;

use App\Models\CalendarEvent;
use App\Models\EventWeeklyAvailability;
use App\Http\Controllers\AppointmentController;
use App\Models\Appointment;
use App\Models\AppointmentBooking;
use App\Models\Booking;
use App\Models\User;
use App\Models\CustomField;
use App\Services\SlotGeneratorService;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class CalendarEventController extends Controller
{
    /**
     * Check if user has calendar event permission based on pricing plan
     */
    private function hasCalendarEventPermission($permission)
    {
        $user = Auth::user();
        
        // System admin and staff have all permissions
        if ($user->type === 'system admin' || $user->type === 'staff') {
            return true;
        }
        
        // For company users, check pricing plan permissions
        if ($user->type === 'company') {
            return $user->hasModulePermission('booking', $permission);
        }
        
        return false;
    }

    public function store(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('create calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        Log::info("Request" , $request->toArray());
        // Validate request
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'duration' => 'nullable|integer|min:1|max:1440',
            'booking_per_slot' => 'nullable|integer|min:1|max:100',
            'minimum_notice' => 'nullable|integer|min:0',
            'minimum_notice_value' => 'nullable|integer|min:0|max:999',
            'minimum_notice_unit' => 'nullable|in:minutes,hours,days,months',
            'description' => 'nullable|string',
            'payment_amount' => 'nullable|numeric|min:0',
            'payment_required' => 'nullable|in:true,false,1,0,"true","false"',
            'payment_currency' => 'nullable|string|max:3',
            'require_name' => 'nullable|in:true,false,1,0,"true","false"',
            'require_email' => 'nullable|in:true,false,1,0,"true","false"',
            'require_phone' => 'nullable|in:true,false,1,0,"true","false"',
            'location' => 'nullable|in:in_person,zoom,skype,meet,phone,others',
            'meet_link' => 'nullable|string|max:500',
            'physical_address' => 'nullable|string',
            'custom_redirect_url' => 'nullable|url|max:500',
            'custom_field' => 'nullable|string|max:255',
            'custom_field_value' => 'nullable|string',
            'custom_fields' => 'nullable|array',
            'custom_fields.*.type' => 'nullable|string|max:255',
            'custom_fields.*.label' => 'nullable|string|max:255',
            'availability' => 'nullable|array',
            'date_override' => 'nullable|array',
            'date_override.*' => 'nullable|string|date_format:Y-m-d\TH:i',
            'assigned_staff_id' => 'nullable|exists:users,id',
            'status' => 'nullable|in:active,inactive',
            'date_range_type' => 'nullable|in:calendar_days,date_range,indefinitely',
            'date_range_days' => 'nullable|integer|min:1|max:365',
            'date_range_start' => 'nullable|date',
            'date_range_end' => 'nullable|date|after_or_equal:date_range_start',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();
            \Log::info('Incoming request data:', $request->all());
            // Process custom fields (field IDs from CustomField model)
            $customFields = [];

            if ($request->has('custom_fields') && is_array($request->custom_fields)) {
                // Get custom field details from the database
                $customFieldIds = array_filter($request->custom_fields);
                if (!empty($customFieldIds)) {
                    $customFieldModels = CustomField::whereIn('id', $customFieldIds)
                        ->where('module', 'booking')
                        ->where('created_by', Auth::user()->creatorId())
                        ->where('status', true)
                        ->get();
                    
                    foreach ($customFieldModels as $field) {
                        $customFields[] = [
                            'id' => $field->id,
                            'name' => $field->name,
                            'type' => $field->type,
                            'options' => $field->options
                        ];
                    }
                }
            }

            // Create the calendar event
            $calendarEvent = CalendarEvent::create([
                'title' => $request->title,
                'start_date' => $request->start_date ?? now(),
                'end_date' => $request->end_date ?? now()->addYear(),
                'duration' => $request->duration,
                'booking_per_slot' => $request->booking_per_slot ?? 1,
                'minimum_notice' => $request->minimum_notice ?? 0,
                'minimum_notice_value' => $request->minimum_notice_value,
                'minimum_notice_unit' => $request->minimum_notice_unit ?? 'minutes',
                'description' => $request->description,
                'payment_amount' => $request->payment_amount,
                'payment_required' => filter_var($request->payment_required, FILTER_VALIDATE_BOOLEAN),
                'payment_currency' => $request->payment_currency ?? 'USD',
                'require_name' => filter_var($request->require_name, FILTER_VALIDATE_BOOLEAN),
                'require_email' => filter_var($request->require_email, FILTER_VALIDATE_BOOLEAN),
                'require_phone' => filter_var($request->require_phone, FILTER_VALIDATE_BOOLEAN),
                'location' => $request->location,
                'locations_data' => $request->locations_data,
                'custom_redirect_url' => $request->custom_redirect_url,
                'meet_link' => $request->meet_link,
                'physical_address' => $request->physical_address,
                'custom_field' => $request->custom_field, // Single field for backward compatibility
                'custom_field_value' => null, // No values stored, will be used during booking
                'custom_fields' => !empty($customFields) ? $customFields : null, // Multiple field names only
                'date_override' => !empty($request->input('date_override')) ? json_encode($request->input('date_override')) : null,// Store date overrides as JSON
                'assigned_staff_id' => $request->assigned_staff_id,
                'status' => $request->status ?? 'active',
                'date_range_type' => $request->date_range_type ?? 'indefinitely',
                'date_range_days' => $request->date_range_type === 'calendar_days' ? $request->date_range_days : null,
                'date_range_start' => $request->date_range_type === 'date_range' ? $request->date_range_start : null,
                'date_range_end' => $request->date_range_type === 'date_range' ? $request->date_range_end : null,
                'created_by' => Auth::id()
            ]);
           

            // Save weekly availability in JSON format
            if ($request->has('availability')) {
                $weeklyAvailability = $request->input('availability');
                \Log::info('Weekly availability data:', $weeklyAvailability);

                // Store in JSON field
                $calendarEvent->update([
                    'weekly_availability' => $weeklyAvailability
                ]);

                // Also save to separate table for backward compatibility
                foreach ($weeklyAvailability as $day => $dayData) {
                    \Log::info("Processing day: $day", $dayData);

                    if (isset($dayData['enabled']) && $dayData['enabled']) {
                        if (isset($dayData['slots']) && is_array($dayData['slots'])) {
                            foreach ($dayData['slots'] as $slot) {
                                \Log::info("Creating slot for $day:", $slot);

                                EventWeeklyAvailability::create([
                                    'calendar_event_id' => $calendarEvent->id,
                                    'day_of_week' => $day,
                                    'start_time' => $slot['start'],
                                    'end_time' => $slot['end']
                                ]);
                            }
                        }
                    }
                }
            }

            // Generate appointment booking slots based on weekly availability
            try {
                $slotGenerator = new SlotGeneratorService();
                $slotsCreated = $slotGenerator->generateSlotsForEvent($calendarEvent);
                \Log::info("Generated {$slotsCreated} appointment slots for event ID: {$calendarEvent->id}");
            } catch (\Exception $e) {
                \Log::error("Failed to generate slots for event ID: {$calendarEvent->id} - " . $e->getMessage());
                // Don't fail the entire event creation if slot generation fails
                $slotsCreated = 0;
            }

            DB::commit();

            // Dispatch event created webhook
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $eventData = [
                    'id' => $calendarEvent->id,
                    'title' => $calendarEvent->title,
                    'start_date' => $calendarEvent->start_date,
                    'end_date' => $calendarEvent->end_date,
                    'duration' => $calendarEvent->duration,
                    'booking_per_slot' => $calendarEvent->booking_per_slot,
                    'minimum_notice' => $calendarEvent->minimum_notice,
                    'description' => $calendarEvent->description,
                    'location' => $calendarEvent->location,
                    'meet_link' => $calendarEvent->meet_link,
                    'physical_address' => $calendarEvent->physical_address,
                    'custom_fields' => $calendarEvent->custom_fields,
                    'date_override' => $calendarEvent->date_override,
                    'created_by' => $calendarEvent->created_by,
                    'slots_created' => $slotsCreated
                ];
                $webhookDispatcher->dispatchEventCreated($eventData);
            } catch (\Exception $e) {
                Log::error('Calendar event creation webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            return response()->json([
                'success' => true,
                'message' => 'Event created successfully',
                'data' => $calendarEvent,
                'slots_created' => $slotsCreated
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating calendar event: ' . $e->getMessage()); // Log the error
            
            return response()->json([
                'success' => false,
                'message' => 'Error creating calendar event: ' . $e->getMessage()
            ], 500);
        }
    }

    public function index(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        // Get pagination parameters
        $perPage = $request->get('per_page', 10); // Default 10 items per page
        $page = $request->get('page', 1);

        // Validate per_page parameter
        $perPage = in_array($perPage, [5, 10, 25, 50, 100]) ? $perPage : 10;

        // Get paginated events
        $eventsQuery = CalendarEvent::where('created_by', Auth::id())
            ->with('assignedStaff')
            ->orderBy('created_at', 'desc');

        $events = $eventsQuery->paginate($perPage, ['*'], 'page', $page);

        // Add booking count to each event
        foreach ($events as $event) {
            $bookings = Booking::where('event_id', $event->id)->get();
            $event->bookings = $bookings;
            $event->booking_count = $bookings->count();
        }

        return response()->json([
            'success' => true,
            'data' => $events->items(),
            'pagination' => [
                'current_page' => $events->currentPage(),
                'last_page' => $events->lastPage(),
                'per_page' => $events->perPage(),
                'total' => $events->total(),
                'from' => $events->firstItem(),
                'to' => $events->lastItem(),
                'has_more_pages' => $events->hasMorePages(),
                'prev_page_url' => $events->previousPageUrl(),
                'next_page_url' => $events->nextPageUrl()
            ]
        ]);
    }

    /**
     * Bulk update status for multiple events
     */
    public function bulkUpdateStatus(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('edit calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        $request->validate([
            'event_ids' => 'required|array|min:1',
            'event_ids.*' => 'integer|exists:calendar_events,id',
            'status' => 'required|in:active,inactive'
        ]);

        try {
            $eventIds = $request->event_ids;
            $status = $request->status;

            // Update only events owned by the current user
            $updatedCount = CalendarEvent::where('created_by', Auth::id())
                ->whereIn('id', $eventIds)
                ->update(['status' => $status]);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updatedCount} event(s) to {$status}.",
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating events: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete multiple events
     */
    public function bulkDelete(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('delete calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        $request->validate([
            'event_ids' => 'required|array|min:1',
            'event_ids.*' => 'integer|exists:calendar_events,id'
        ]);

        try {
            $eventIds = $request->event_ids;

            // Delete only events owned by the current user
            $deletedCount = CalendarEvent::where('created_by', Auth::id())
                ->whereIn('id', $eventIds)
                ->delete();

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedCount} event(s).",
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting events: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('show calendar event')) {
            return redirect()->back()->with('error', 'Unauthorized - Calendar event module not available in your plan.');
        }

        try {
            $event = CalendarEvent::where('created_by', Auth::id())
                ->with(['weeklyAvailability', 'bookings'])
                ->findOrFail($id);

            return view('tasks.copy_event', [
                'event' => $event
            ]);
        } catch (ModelNotFoundException $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Event not found.');
        } catch (\Exception $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Error loading event details: ' . $e->getMessage());
        }
    }

    /**
     * Get event data for editing (AJAX endpoint)
     */
    public function getEventData($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        try {
            $event = CalendarEvent::where('created_by', Auth::id())
                ->findOrFail($id);

            // Load custom fields for booking module
            $customFields = CustomField::where('module', 'booking')
                ->where('created_by', Auth::user()->creatorId())
                ->where('status', true)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $event,
                'custom_fields' => $customFields
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Event not found.'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading event details: ' . $e->getMessage()
            ], 500);
        }
    }
    
    public function view($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('show calendar event')) {
            return redirect()->back()->with('error', 'Unauthorized - Calendar event module not available in your plan.');
        }
        
        try {
            $event = CalendarEvent::where('created_by', Auth::id())
                ->with('weeklyAvailability')
                ->findOrFail($id);
 
            return view('copy_event', [
                'event' => $event
            ]);
        } catch (ModelNotFoundException $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Event not found.');
        } catch (\Exception $e) {
            return redirect()->route('calendar-events.index')->with('error', 'Error loading event details: ' . $e->getMessage());
        }
    }

    /**
     * Public view for sharing event links (no authentication required)
     */
    public function publicView($id)
    {
        try {
            $event = CalendarEvent::with(['weeklyAvailability', 'bookings'])->findOrFail($id);
            
            // Get payment settings for the event creator
            $company_payment_setting = \App\Models\Utility::getCompanyPaymentSetting($event->created_by);

            return view('tasks.copy_event', [
                'event' => $event,
                'company_payment_setting' => $company_payment_setting
            ]);
        } catch (ModelNotFoundException $e) {
            abort(404, 'Event not found.');
        } catch (\Exception $e) {
            abort(500, 'Error loading event details.');
        }
    }

    public function update(Request $request, $id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('edit calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'duration' => 'nullable|integer|min:1|max:1440',
            'booking_per_slot' => 'nullable|integer|min:1|max:100',
            'minimum_notice' => 'nullable|integer|min:0',
            'minimum_notice_value' => 'nullable|integer|min:0|max:999',
            'minimum_notice_unit' => 'nullable|in:minutes,hours,days,months',
            'description' => 'nullable|string',
            'payment_amount' => 'nullable|numeric|min:0',
            'payment_required' => 'nullable|in:true,false,1,0,"true","false"',
            'payment_currency' => 'nullable|string|max:3',
            'require_name' => 'nullable|in:true,false,1,0,"true","false"',
            'require_email' => 'nullable|in:true,false,1,0,"true","false"',
            'require_phone' => 'nullable|in:true,false,1,0,"true","false"',
            'location' => 'nullable|in:in_person,zoom,skype,meet,phone,others',
            'meet_link' => 'nullable|string|max:500',
            'physical_address' => 'nullable|string',
            'custom_redirect_url' => 'nullable|url|max:500',
            'custom_field' => 'nullable|string',
            'custom_field_value' => 'nullable|string',
            'date_override' => 'nullable|array',
            'date_override.*' => 'date_format:Y-m-d\TH:i',
            'assigned_staff_id' => 'nullable|exists:users,id',
            'status' => 'nullable|in:active,inactive',
            'date_range_type' => 'nullable|in:calendar_days,date_range,indefinitely',
            'date_range_days' => 'nullable|integer|min:1|max:365',
            'date_range_start' => 'nullable|date',
            'date_range_end' => 'nullable|date|after_or_equal:date_range_start',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $calendarEvent = CalendarEvent::where('created_by', Auth::id())->findOrFail($id);

            // Process custom fields (field IDs from CustomField model)
            $customFields = [];
            if ($request->has('custom_fields') && is_array($request->custom_fields)) {
                // Get custom field details from the database
                $customFieldIds = array_filter($request->custom_fields);
                if (!empty($customFieldIds)) {
                    $customFieldModels = CustomField::whereIn('id', $customFieldIds)
                        ->where('module', 'booking')
                        ->where('created_by', Auth::user()->creatorId())
                        ->where('status', true)
                        ->get();
                    
                    foreach ($customFieldModels as $field) {
                        $customFields[] = [
                            'id' => $field->id,
                            'name' => $field->name,
                            'type' => $field->type,
                            'options' => $field->options
                        ];
                    }
                }
            }

            $updateData = [
                'title' => $request->title,
                'start_date' => $request->start_date ?? $calendarEvent->start_date ?? now(),
                'end_date' => $request->end_date ?? $calendarEvent->end_date ?? now()->addYear(),
                'duration' => $request->duration,
                'booking_per_slot' => $request->booking_per_slot ?? 1,
                'minimum_notice' => $request->minimum_notice ?? 0,
                'minimum_notice_value' => $request->minimum_notice_value,
                'minimum_notice_unit' => $request->minimum_notice_unit ?? 'minutes',
                'description' => $request->description,
                'payment_amount' => $request->payment_amount,
                'payment_required' => filter_var($request->payment_required, FILTER_VALIDATE_BOOLEAN),
                'payment_currency' => $request->payment_currency ?? 'USD',
                'location' => $request->location,
                'locations_data' => $request->locations_data,
                'custom_redirect_url' => $request->custom_redirect_url,
                'meet_link' => $request->meet_link,
                'physical_address' => $request->physical_address,
                'date_range_type' => $request->date_range_type ?? 'indefinitely',
                'date_range_days' => $request->date_range_type === 'calendar_days' ? $request->date_range_days : null,
                'date_range_start' => $request->date_range_type === 'date_range' ? $request->date_range_start : null,
                'date_range_end' => $request->date_range_type === 'date_range' ? $request->date_range_end : null,
                'require_name' => filter_var($request->require_name, FILTER_VALIDATE_BOOLEAN),
                'require_email' => filter_var($request->require_email, FILTER_VALIDATE_BOOLEAN),
                'require_phone' => filter_var($request->require_phone, FILTER_VALIDATE_BOOLEAN),
                'custom_field' => $request->custom_field,
                'custom_field_value' => $request->custom_field_value,
                'custom_fields' => !empty($customFields) ? $customFields : null,
                'date_override' => !empty($request->input('date_override')) ? json_encode($request->input('date_override')) : null,
                'assigned_staff_id' => $request->assigned_staff_id,
                'status' => $request->status ?? $calendarEvent->status ?? 'active',
            ];

            // Update calendar event with all fields
            $calendarEvent->update($updateData);

            // Handle weekly availability updates
            if ($request->has('availability')) {
                $weeklyAvailability = $request->input('availability');

                // Store in JSON field
                $calendarEvent->update([
                    'weekly_availability' => $weeklyAvailability
                ]);

                // Also update separate table for backward compatibility
                $calendarEvent->weeklyAvailability()->delete(); // Clear existing availability

                foreach ($weeklyAvailability as $day => $dayData) {
                    if (isset($dayData['enabled']) && $dayData['enabled']) {
                        foreach ($dayData['slots'] as $slot) {
                            EventWeeklyAvailability::create([
                                'calendar_event_id' => $calendarEvent->id,
                                'day_of_week' => $day,
                                'start_time' => $slot['start'],
                                'end_time' => $slot['end']
                            ]);
                        }
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Calendar event updated successfully!',
                'data' => $calendarEvent->fresh()->load('weeklyAvailability') // Using fresh() to get updated data
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error updating calendar event: ' . $e->getMessage()); // Log the error
            
            return response()->json([
                'success' => false,
                'message' => 'Error updating calendar event: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('delete calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }
        
        try {
            $calendarEvent = CalendarEvent::where('created_by', Auth::id())->findOrFail($id);
            $calendarEvent->delete();

            return response()->json([
                'success' => true,
                'message' => 'Calendar event deleted successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting calendar event: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getCalendarData(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }
        
        $eventFilter = $request->input('event_filter');
        $staffFilter = $request->input('staff_filter');
        
        $arrayJson = [];

        // Don't show events in calendar - only show bookings
        // Events are only used for filtering bookings

        // Add bookings from bookings table
        $bookingsQuery = \App\Models\Booking::whereHas('event', function($query) {
            $query->where('created_by', Auth::id());
        })->with('event');
        
        // Apply event filter if specified
        if (!empty($eventFilter)) {
            $bookingsQuery->where('event_id', $eventFilter);
        }
        
        // Apply staff filter if specified
        if (!empty($staffFilter)) {
            $bookingsQuery->whereHas('event', function($query) use ($staffFilter) {
                $query->where('assigned_staff_id', $staffFilter);
            });
        }
        
        $bookings = $bookingsQuery->get();

        foreach ($bookings as $booking) {
            $startDateTime = $booking->date . ' ' . ($booking->time ?? '00:00:00');
            $endDateTime = date('Y-m-d H:i:s', strtotime($startDateTime . ' +' . ($booking->event->duration ?? 60) . ' minutes'));

            // Determine colors based on booking status
            $status = $booking->status ?? 'scheduled';
            $colors = $this->getStatusColors($status);

            $arrayJson[] = [
                'id' => 'booking_' . $booking->id,
                'title' => '📅 ' . $booking->name . ' - ' . $booking->event->title,
                'start' => $startDateTime,
                'end' => $endDateTime,
                'className' => 'appointment-booking',
                'backgroundColor' => $colors['background'],
                'borderColor' => $colors['border'],
                'textColor' => $colors['text'],
                'allDay' => false,
                'extendedProps' => [
                    'type' => 'booking',
                    'booking_id' => $booking->id,
                    'event_id' => $booking->event_id,
                    'contact_name' => $booking->name,
                    'email' => $booking->email,
                    'phone' => $booking->phone,
                    'event_title' => $booking->event->title,
                    'time' => $booking->time,
                    'status' => $status,
                    'custom_fields' => $booking->custom_fields,
                    'custom_fields_value' => $booking->custom_fields_value
                ]
            ];
        }

        // Add appointments from appointment_booking table
        $appointmentBookingsQuery = \App\Models\AppointmentBooking::whereHas('event', function($query) {
            $query->where('created_by', Auth::id());
        })->with('event');
        
        // Apply event filter if specified
        if (!empty($eventFilter)) {
            $appointmentBookingsQuery->where('event_id', $eventFilter);
        }
        
        // Apply staff filter if specified
        if (!empty($staffFilter)) {
            $appointmentBookingsQuery->whereHas('event', function($query) use ($staffFilter) {
                $query->where('assigned_staff_id', $staffFilter);
            });
        }
        
        $appointmentBookings = $appointmentBookingsQuery->get();

        foreach ($appointmentBookings as $appointmentBooking) {
            $startDateTime = $appointmentBooking->event_date . ' ' . $appointmentBooking->time_slots;
            $endDateTime = date('Y-m-d H:i:s', strtotime($startDateTime . ' +' . ($appointmentBooking->event->duration ?? 60) . ' minutes'));

            $arrayJson[] = [
                'id' => 'appointment_' . $appointmentBooking->id,
                'title' => '🗓️ Appointment - ' . $appointmentBooking->event->title,
                'start' => $startDateTime,
                'end' => $endDateTime,
                'className' => 'appointment-booking-new',
                'backgroundColor' => '#17a2b8',
                'borderColor' => '#17a2b8',
                'textColor' => '#ffffff',
                'allDay' => false,
                'extendedProps' => [
                    'type' => 'appointment_booking',
                    'appointment_id' => $appointmentBooking->id,
                    'event_id' => $appointmentBooking->event_id,
                    'event_title' => $appointmentBooking->event->title,
                    'event_location' => $appointmentBooking->event_location,
                    'event_location_value' => $appointmentBooking->event_location_value,
                    'time_zone' => $appointmentBooking->time_zone
                ]
            ];
        }

        return response()->json($arrayJson);
    }

    public function getAvailableSlots(Request $request)
    {
        $date = $request->input('date');
        // Logic to fetch available time slots for the given date
        $slots = Appointment::getAvailableSlots($date); // Adjust as per your logic
        return response()->json(['slots' => $slots]);
    }
    
    public function cancelAppointment(Request $request)
    {
        $slotId = $request->input('slot_id');
        $date = $request->input('date');
        // Logic to cancel the appointment
        $appointment = Appointment::find($slotId);
        if ($appointment) {
            $appointment->delete(); // Or mark as cancelled
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false, 'message' => 'Appointment not found.'], 404);
    }

    /**
     * Get available events for a specific date
     */
    public function getAvailableEvents(Request $request)
    {
        try {
            $date = $request->input('date');
            $userId = Auth::id();

            \Log::info('Getting available events for user: ' . $userId);

            // Build query for events that belong to the current user
            $query = CalendarEvent::where('created_by', $userId);

            // If date is provided, filter by date range
            if ($date) {
                $query->whereDate('start_date', '<=', $date)
                      ->whereDate('end_date', '>=', $date);
                \Log::info('Filtering events by date: ' . $date);
            } else {
                // If no date provided, get all future events
                $query->where('end_date', '>=', now());
                \Log::info('Getting all future events');
            }

            // Get events for the specified date that belong to the current user
            $query = CalendarEvent::where('created_by', Auth::id())
                ->where('status', 'active'); // Only active events

            // Apply date range constraints based on date_range_type
            $query->where(function($q) use ($date) {
                $requestDate = \Carbon\Carbon::parse($date);
                $today = \Carbon\Carbon::today();

                // Calendar days constraint
                $q->where(function($subQ) use ($requestDate, $today) {
                    $subQ->where('date_range_type', 'calendar_days')
                         ->whereRaw('DATE_ADD(CURDATE(), INTERVAL date_range_days DAY) >= ?', [$requestDate->format('Y-m-d')])
                         ->where('date_range_days', '>=', $requestDate->diffInDays($today));
                })
                // Date range constraint
                ->orWhere(function($subQ) use ($requestDate) {
                    $subQ->where('date_range_type', 'date_range')
                         ->whereDate('date_range_start', '<=', $requestDate)
                         ->whereDate('date_range_end', '>=', $requestDate);
                })
                // Indefinitely constraint (always available)
                ->orWhere('date_range_type', 'indefinitely')
                // Backward compatibility for events without date_range_type
                ->orWhere(function($subQ) use ($date) {
                    $subQ->whereNull('date_range_type')
                         ->whereDate('start_date', '<=', $date)
                         ->whereDate('end_date', '>=', $date);
                });
            });

            $events = $query->select('id', 'title', 'duration', 'location', 'physical_address', 'meet_link', 'date_range_type', 'date_range_days', 'date_range_start', 'date_range_end')
                           ->get();

            // Add location_value to each event
            $events->each(function ($event) {
                if ($event->location === 'in_person') {
                    $event->location_value = $event->physical_address ?: 'Address not specified';
                } else {
                    $event->location_value = $event->meet_link ?: 'Link not specified';
                }
            });

            return response()->json([
                'success' => true,
                'data' => $events,
                'message' => 'Events fetched successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching available events: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch available events: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get events for filter dropdown
     */
    public function getEventsForFilter()
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        $events = CalendarEvent::where('created_by', Auth::id())
            ->where('status', 'active')
            ->select('id', 'title')
            ->orderBy('title')
            ->get();

        return response()->json([
            'success' => true,
            'events' => $events
        ]);
    }

    /**
     * Get staff for filter dropdown
     */
    public function getStaffForFilter()
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Calendar event module not available in your plan.'
            ], 403);
        }

        // Get current user's company ID
        $companyId = Auth::user()->creatorId();

        // Get staff and employees from the same company
        $staffData = User::where('created_by', $companyId)
            ->whereIn('type', ['staff', 'Employee', 'employee', 'client'])
            ->select('id', 'name', 'email', 'type')
            ->orderBy('type')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'staff' => $staffData
        ]);
    }

    /**
     * Get available time slots for a specific event and date
     */
    public function getTimeSlots(Request $request)
    {
        try {
            $eventId = $request->input('event_id');
            $date = $request->input('date');

            if (!$eventId || !$date) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event ID and date are required'
                ], 400);
            }

            // Get the event
            $event = CalendarEvent::where('id', $eventId)
                ->where('created_by', Auth::id())
                ->first();

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            // Get existing bookings for this event and date
            $existingBookings = Booking::where('event_id', $eventId)
                ->where('date', $date)
                ->pluck('time')
                ->toArray();

            // Generate time slots based on event duration (ensure it's an integer)
            $duration = (int) ($event->duration ?: 60); // Default to 60 minutes
            $slots = [];

            // Generate slots from 9 AM to 5 PM
            $startHour = 9;
            $endHour = 17;

            for ($hour = $startHour; $hour < $endHour; $hour++) {
                for ($minute = 0; $minute < 60; $minute += $duration) {
                    if ($hour === $endHour - 1 && $minute + $duration > 60) break;

                    $timeString = sprintf('%02d:%02d', $hour, $minute);

                    // Skip if this time slot is already booked
                    if (!in_array($timeString, $existingBookings)) {
                        $displayTime = $this->formatTime12Hour($timeString);
                        $slots[] = [
                            'time' => $timeString,
                            'display_time' => $displayTime
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $slots
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch time slots: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to format time to 12-hour format
     */
    private function formatTime12Hour($time24)
    {
        $time = \DateTime::createFromFormat('H:i', $time24);
        return $time->format('g:i A');
    }

    /**
     * Helper method to get colors based on booking status
     */
    private function getStatusColors($status)
    {
        switch ($status) {
            case 'cancelled':
                return [
                    'background' => '#dc3545',
                    'border' => '#dc3545',
                    'text' => '#ffffff'
                ];
            case 'show_up':
                return [
                    'background' => '#28a745',
                    'border' => '#28a745',
                    'text' => '#ffffff'
                ];
            case 'no_show':
                return [
                    'background' => '#ffc107',
                    'border' => '#ffc107',
                    'text' => '#000000'
                ];
            case 'scheduled':
            case 'booked':
            default:
                return [
                    'background' => '#007bff',
                    'border' => '#007bff',
                    'text' => '#ffffff'
                ];
        }
    }

    /**
     * Show the calendar view
     */
    public function showCalendarView()
    {
        // Check permission based on pricing plan
        if (!$this->hasCalendarEventPermission('view calendar event')) {
            return redirect()->back()->with('error', 'Unauthorized - Calendar event module not available in your plan.');
        }

        // Return the calendar events view
        return view('tasks.calendar');
    }

    /**
     * Get custom fields for booking module
     */
    public function getBookingCustomFields()
    {
        try {
            // Check permission based on pricing plan
            if (!$this->hasCalendarEventPermission('view calendar event')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized - Calendar event module not available in your plan.'
                ], 403);
            }

            // Load custom fields for booking module
            $customFields = CustomField::where('module', 'booking')
                ->where('created_by', Auth::user()->creatorId())
                ->where('status', true)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $customFields
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching booking custom fields: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch custom fields: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get staff data for assignment dropdown
     */
    public function getStaffData()
    {
        try {
            // Get current user's company ID
            $companyId = Auth::user()->creatorId();

            // Debug: Log the company ID and current user info
            \Log::info('Staff Data Request - Company ID: ' . $companyId . ', User ID: ' . Auth::id() . ', User Type: ' . Auth::user()->type);

            // Get staff and employees from the same company
            $staffData = User::where('created_by', $companyId)
                ->whereIn('type', ['staff', 'Employee', 'employee', 'client'])
                ->select('id', 'name', 'email', 'type')
                ->orderBy('type')
                ->orderBy('name')
                ->get();

            // Debug: Log the found staff data
            \Log::info('Found staff data: ' . $staffData->count() . ' records');
            \Log::info('Staff data: ' . $staffData->toJson());

            return response()->json([
                'success' => true,
                'data' => $staffData
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching staff data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch staff data: ' . $e->getMessage()
            ], 500);
        }
    }
}
